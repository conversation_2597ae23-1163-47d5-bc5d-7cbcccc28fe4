<script setup lang="ts">
import { getCurrentWindow } from '@tauri-apps/api/window'

const appWindow = getCurrentWindow()

// 窗口控制函数
const minimizeWindow = () => {
  appWindow.minimize()
}

const toggleMaximize = () => {
  appWindow.toggleMaximize()
}

const closeWindow = () => {
  appWindow.close()
}
</script>

<template>
  <div data-tauri-drag-region class="titlebar flex justify-between items-center shadow">
    <!-- 应用标题 -->
    <div class="title text-14px font-medium m-l-12px">
      青山补货助手
    </div>

    <!-- 中间插槽 -->
    <div class="center">
      <slot></slot>
    </div>

    <!-- 窗口控制按钮 -->
    <div class="control flex items-center">
      <slot name="control"></slot>
      <div class="item" @click="minimizeWindow" title="最小化">
        <div class="i-carbon-minimize text-16px"></div>
      </div>
      <div class="item" @click="toggleMaximize" title="最大化/还原">
        <div class="i-carbon-maximize text-16px"></div>
      </div>
      <div class="item close" @click="closeWindow" title="关闭">
        <div class="i-carbon-close text-16px"></div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.titlebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 36px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .control {
    .item {
      display: inline-flex;
      width: 42px;
      height: 36px;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &.close:hover {
        background: #e74c3c;
      }
    }
  }
}

// 为页面内容添加顶部边距，避免被标题栏遮挡
:global(.page) {
  padding-top: 40px;
}
</style>