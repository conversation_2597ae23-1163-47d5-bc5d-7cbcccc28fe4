<script setup lang="ts">
import { getCurrentWindow } from '@tauri-apps/api/window'
import { ref, onMounted, onUnmounted } from 'vue'

const appWindow = getCurrentWindow()
const isMaximized = ref(false)

// 窗口控制函数
const minimizeWindow = () => {
  appWindow.minimize()
}

const toggleMaximize = () => {
  appWindow.toggleMaximize()
}

const closeWindow = () => {
  appWindow.close()
}

// 监听窗口状态变化
let unlistenResize: (() => void) | null = null

onMounted(async () => {
  // 获取初始状态
  isMaximized.value = await appWindow.isMaximized()

  // 监听窗口大小变化
  unlistenResize = await appWindow.onResized(async () => {
    isMaximized.value = await appWindow.isMaximized()
  })
})

onUnmounted(() => {
  if (unlistenResize) {
    unlistenResize()
  }
})
</script>

<template>
  <div data-tauri-drag-region class="titlebar flex justify-between items-center shadow">
    <!-- 应用标题 -->
    <div class="left text-14px font-medium m-l-12px">
      青山补货助手
    </div>

    <!-- 中间插槽 -->
    <div class="center">
      <slot></slot>
    </div>

    <!-- 窗口控制按钮 -->
    <div class="right flex items-center">
      <slot name="right"></slot>
      <div class="item" @click="minimizeWindow" title="最小化">
        <div class="minimize-icon"></div>
      </div>
      <div class="item" @click="toggleMaximize" :title="isMaximized ? '还原' : '最大化'">
        <div v-if="isMaximized" class="restore-icon"></div>
        <div v-else class="maximize-icon"></div>
      </div>
      <div class="item close" @click="closeWindow" title="关闭">
        <div class="close-icon"></div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.titlebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 36px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --titlebar-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .right {
    .item {
      display: inline-flex;
      width: 42px;
      height: 36px;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &.close:hover {
        background: #e74c3c;
      }

      // Windows风格的图标
      .minimize-icon {
        width: 10px;
        height: 1px;
        background: currentColor;
      }

      .maximize-icon {
        width: 10px;
        height: 10px;
        border: 1px solid currentColor;
        background: transparent;
      }

      .restore-icon {
        width: 10px;
        height: 10px;
        position: relative;

        &::before,
        &::after {
          content: '';
          position: absolute;
          border: 1px solid currentColor;
          background: transparent;
        }

        // 后面的窗口
        &::before {
          width: 7px;
          height: 7px;
          top: 0;
          right: 0;
        }

        // 前面的窗口
        &::after {
          width: 7px;
          height: 7px;
          bottom: 0;
          left: 0;
          background: var(--titlebar-bg, transparent);
        }
      }

      .close-icon {
        width: 10px;
        height: 10px;
        position: relative;

        &::before,
        &::after {
          content: '';
          position: absolute;
          width: 10px;
          height: 1px;
          background: currentColor;
          top: 50%;
          left: 0;
          transform-origin: center;
        }

        &::before {
          transform: translateY(-50%) rotate(45deg);
        }

        &::after {
          transform: translateY(-50%) rotate(-45deg);
        }
      }
    }
  }
}

// 为页面内容添加顶部边距，避免被标题栏遮挡
:global(.page) {
  padding-top: 40px;
}
</style>