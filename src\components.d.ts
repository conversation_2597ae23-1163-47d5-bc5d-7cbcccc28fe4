/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    copy: typeof import('./components/DarkToggle copy.vue')['default']
    DarkToggle: typeof import('./components/DarkToggle.vue')['default']
    GlobalSetting: typeof import('./components/GlobalSetting.vue')['default']
    HeaderBar: typeof import('./components/HeaderBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TitleBar: typeof import('./components/TitleBar.vue')['default']
  }
}
