<script setup lang="ts">
import { Message } from '@arco-design/web-vue'

function globalSetting() {
  Message.info('This is an info message')
}
</script>

<template>
  <div class="globalSetting" title="全局设置" @click="globalSetting">
    <div class="i-carbon-settings text-16px"></div>
  </div>
</template>

<style scoped lang="less">
.globalSetting {
  display: inline-flex;
  width: 42px;
  height: 36px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>